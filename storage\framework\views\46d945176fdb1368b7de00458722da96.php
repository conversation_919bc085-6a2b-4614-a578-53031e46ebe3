<?php $__env->startSection('title', '<PERSON><PERSON>'); ?>

<?php $__env->startSection('content'); ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><PERSON><PERSON> Detayı</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Ana Sayfa</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dealers.index')); ?>">Bayi Yönetimi</a></li>
                    <li class="breadcrumb-item active"><PERSON><PERSON> Detayı</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title"><?php echo e($dealer->name); ?></h3>
                        <div class="card-tools">
                            <?php if($dealer->status): ?>
                                <span class="badge badge-success">Aktif</span>
                            <?php else: ?>
                                <span class="badge badge-danger">Pasif</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Bayi Adı:</label>
                                    <p><?php echo e($dealer->name); ?></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Bölge:</label>
                                    <p><?php echo e($dealer->region->name ?? '-'); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>İletişim Kişisi:</label>
                                    <p><?php echo e($dealer->contact_person ?? '-'); ?></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Telefon:</label>
                                    <p><?php echo e($dealer->phone ?? '-'); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>E-posta:</label>
                                    <p><?php echo e($dealer->email ?? '-'); ?></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Durum:</label>
                                    <p>
                                        <?php if($dealer->status): ?>
                                            <span class="badge badge-success">Aktif</span>
                                        <?php else: ?>
                                            <span class="badge badge-danger">Pasif</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Şehir:</label>
                                    <p><?php echo e($dealer->city ?? '-'); ?></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>İlçe:</label>
                                    <p><?php echo e($dealer->district ?? '-'); ?></p>
                                </div>
                            </div>
                        </div>

                        <?php if($dealer->address): ?>
                        <div class="row">
                            <div class="col-12">
                                <div class="info-group">
                                    <label>Adres:</label>
                                    <p><?php echo e($dealer->address); ?></p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Oluşturulma Tarihi:</label>
                                    <p><?php echo e($dealer->created_at->format('d.m.Y H:i')); ?></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label>Son Güncelleme:</label>
                                    <p><?php echo e($dealer->updated_at->format('d.m.Y H:i')); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer d-flex gap-2">
                        <a href="<?php echo e(route('dealers.edit', $dealer->id)); ?>" class="btn btn-primary flex-fill">Düzenle</a>
                        <a href="<?php echo e(route('dealers.index')); ?>" class="btn btn-secondary flex-fill">Geri Dön</a>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Müşteriler (<?php echo e($dealer->customers->count()); ?>)</h3>
                    </div>
                    <div class="card-body">
                        <?php $__empty_1 = true; $__currentLoopData = $dealer->customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong><?php echo e($customer->company_name ?? $customer->authorized_first_name . ' ' . $customer->authorized_last_name); ?></strong><br>
                                    <small class="text-muted"><?php echo e($customer->email); ?></small>
                                </div>
                                <div>
                                    <a href="<?php echo e(route('customers.show', $customer->id)); ?>" class="btn btn-sm btn-info">Detay</a>
                                </div>
                            </div>
                            <hr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <p class="text-muted">Bu bayiye henüz müşteri atanmamış.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.info-group {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 10px;
}

.info-group label {
    font-size: 0.9rem;
    margin-bottom: 5px;
    display: block;
}

.info-group p {
    font-size: 1rem;
    margin: 0;
    color: #333;
}

.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/dealers/show.blade.php ENDPATH**/ ?>