<?php $__env->startSection('content'); ?>
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="text-center mb-4"><PERSON><PERSON><PERSON> - <?php echo e($customer->company_name); ?></h3>
                </div>
                <div class="card-body">
            <form action="<?php echo e(route('customers.customer-followups.update', [$customer->id, $followup->id])); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                
                <!-- Visit Information Section -->
                <h5 class="mb-3 text-primary"><PERSON><PERSON><PERSON></h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="track_date" class="form-label"><PERSON><PERSON><PERSON></label>
                        <input type="date" class="form-control" id="track_date" name="track_date" value="<?php echo e(old('track_date', \Carbon\Carbon::parse($followup->track_date)->format('Y-m-d'))); ?>" required>
                        <?php $__errorArgs = ['track_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="meet_date" class="form-label">Görüşme Tarihi</label>
                        <input type="date" class="form-control" id="meet_date" name="meet_date" value="<?php echo e(old('meet_date', $followup->meet_date ? \Carbon\Carbon::parse($followup->meet_date)->format('Y-m-d') : '')); ?>">
                        <?php $__errorArgs = ['meet_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="conversation_type" class="form-label">Görüşme Türü</label>
                        <select class="form-control" id="conversation_type" name="conversation_type" required>
                            <option value="">Seçiniz</option>
                            <option value="telefon" <?php echo e(old('conversation_type', $followup->conversation_type)==='telefon' ? 'selected' : ''); ?>>Telefon</option>
                            <option value="yerinde" <?php echo e(old('conversation_type', $followup->conversation_type)==='yerinde' ? 'selected' : ''); ?>>Yerinde</option>
                            <option value="bayide" <?php echo e(old('conversation_type', $followup->conversation_type)==='bayide' ? 'selected' : ''); ?>>Bayide</option>
                        </select>
                        <?php $__errorArgs = ['conversation_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
            
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="user_name" class="form-label">Ziyaret Eden Kişi</label>
                        <input type="text" class="form-control" id="user_name" name="user_name" value="<?php echo e(old('user_name', $followup->user_name)); ?>">
                        <?php $__errorArgs = ['user_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">Durum</label>
                        <select class="form-control" id="status" name="status" required>
                            <option value="">Seçiniz</option>
                            <?php $__currentLoopData = \App\Models\CustomerFollowup::$statusOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($value); ?>" <?php echo e(old('status', $followup->status) == $value ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="status_at_meeting_date" class="form-label">Görüşme Tarihindeki Durum</label>
                        <select class="form-control" id="status_at_meeting_date" name="status_at_meeting_date">
                            <option value="">Seçiniz</option>
                            <?php $__currentLoopData = \App\Models\CustomerFollowup::$statusAtMeetingDateOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($value); ?>" <?php echo e(old('status_at_meeting_date', $followup->status_at_meeting_date) == $value ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['status_at_meeting_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="work_type" class="form-label">Çalışma Türü</label>
                        <select class="form-control" id="work_type" name="work_type">
                            <option value="">Seçiniz</option>
                            <?php $__currentLoopData = \App\Models\CustomerFollowup::$workTypeOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($value); ?>" <?php echo e(old('work_type', $followup->work_type) == $value ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['work_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Location Information Section -->
                <h5 class="mb-3 text-primary mt-4">Lokasyon Bilgileri</h5>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="city" class="form-label">Şehir</label>
                        <input type="text" class="form-control" id="city" name="city" value="<?php echo e(old('city', $followup->city)); ?>">
                        <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="district" class="form-label">İlçe</label>
                        <input type="text" class="form-control" id="district" name="district" value="<?php echo e(old('district', $followup->district)); ?>">
                        <?php $__errorArgs = ['district'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="branch_name" class="form-label">Şube Adı</label>
                        <input type="text" class="form-control" id="branch_name" name="branch_name" value="<?php echo e(old('branch_name', $followup->branch_name)); ?>">
                        <?php $__errorArgs = ['branch_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Company Information Section -->
                <h5 class="mb-3 text-primary mt-4">Firma Bilgileri</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="company_name" class="form-label">Firma Adı</label>
                        <input type="text" class="form-control" id="company_name" name="company_name" value="<?php echo e(old('company_name', $followup->company_name)); ?>">
                        <?php $__errorArgs = ['company_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="current_firm" class="form-label">Mevcut Firma</label>
                        <input type="text" class="form-control" id="current_firm" name="current_firm" value="<?php echo e(old('current_firm', $followup->current_firm)); ?>">
                        <?php $__errorArgs = ['current_firm'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="mail" class="form-label">E-posta</label>
                        <input type="email" class="form-control" id="mail" name="mail" value="<?php echo e(old('mail', $followup->mail)); ?>">
                        <?php $__errorArgs = ['mail'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Contact Person Information Section -->
                <h5 class="mb-3 text-primary mt-4">Görüşülen Kişi Bilgileri</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="contact_first_name" class="form-label">Görüşülen Kişi Adı</label>
                        <input type="text" class="form-control" id="contact_first_name" name="contact_first_name" value="<?php echo e(old('contact_first_name', $followup->contact_first_name)); ?>">
                        <?php $__errorArgs = ['contact_first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="contact_last_name" class="form-label">Görüşülen Kişi Soyadı</label>
                        <input type="text" class="form-control" id="contact_last_name" name="contact_last_name" value="<?php echo e(old('contact_last_name', $followup->contact_last_name)); ?>">
                        <?php $__errorArgs = ['contact_last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="contact_title" class="form-label">Görüşülen Kişi Unvanı</label>
                        <input type="text" class="form-control" id="contact_title" name="contact_title" value="<?php echo e(old('contact_title', $followup->contact_title)); ?>">
                        <?php $__errorArgs = ['contact_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="contact_phone" class="form-label">Görüşülen Kişi Telefonu</label>
                        <input type="text" class="form-control inputmask" id="contact_phone" name="contact_phone" value="<?php echo e(old('contact_phone', $followup->contact_phone)); ?>" data-inputmask="'mask': '+99 999 999 99 99'" inputmode="tel" pattern="\+[0-9 ]{13,16}" placeholder="+90 5xx xxx xx xx">
                        <?php $__errorArgs = ['contact_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="contact_email" class="form-label">Görüşülen Kişi Emaili</label>
                        <input type="email" class="form-control" id="contact_email" name="contact_email" value="<?php echo e(old('contact_email', $followup->contact_email)); ?>">
                        <?php $__errorArgs = ['contact_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Branch Information Section -->
                <h5 class="mb-3 text-primary mt-4">Şube Bilgileri</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="current_branch_count" class="form-label">Mevcut Şube Sayısı</label>
                        <input type="number" class="form-control" id="current_branch_count" name="current_branch_count" value="<?php echo e(old('current_branch_count', $followup->current_branch_count)); ?>">
                        <?php $__errorArgs = ['current_branch_count'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="branch_potential" class="form-label">Şube Potansiyeli</label>
                        <input type="number" class="form-control" id="branch_potential" name="branch_potential" value="<?php echo e(old('branch_potential', $followup->branch_potential)); ?>">
                        <?php $__errorArgs = ['branch_potential'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Agreement Information Section -->
                <h5 class="mb-3 text-primary mt-4">Anlaşma Bilgileri</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="agreement_status" class="form-label">Anlaşma Sağlandı mı?</label>
                        <select class="form-control" id="agreement_status" name="agreement_status">
                            <option value="">Seçiniz</option>
                            <option value="1" <?php echo e(old('agreement_status', $followup->agreement_status) == '1' ? 'selected' : ''); ?>>Evet</option>
                            <option value="0" <?php echo e(old('agreement_status', $followup->agreement_status) == '0' ? 'selected' : ''); ?>>Hayır</option>
                        </select>
                        <?php $__errorArgs = ['agreement_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Pluscard yükleme yapıldı mı?</label>
                        <select class="form-control" id="pluscard_been_loaded" name="pluscard_been_loaded" required>
                            <option value="">Seçiniz</option>
                            <option value="1" <?php echo e(old('pluscard_been_loaded', $followup->pluscard_been_loaded) == '1' ? 'selected' : ''); ?>>Evet</option>
                            <option value="0" <?php echo e(old('pluscard_been_loaded', $followup->pluscard_been_loaded) == '0' ? 'selected' : ''); ?>>Hayır</option>
                        </select>
                        <?php $__errorArgs = ['pluscard_been_loaded'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="number_of_customers_loaded" class="form-label">Yükleme Yapılan Müşteri Sayısı</label>
                        <input type="number" class="form-control" id="number_of_customers_loaded" name="number_of_customers_loaded" min="1" value="<?php echo e(old('number_of_customers_loaded', $followup->number_of_customers_loaded)); ?>">
                        <?php $__errorArgs = ['number_of_customers_loaded'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="loading_amount" class="form-label">Yükleme Miktarı</label>
                        <input type="number" step="0.01" class="form-control" id="loading_amount" name="loading_amount" value="<?php echo e(old('loading_amount', $followup->loading_amount)); ?>">
                        <?php $__errorArgs = ['loading_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="reason_not_understanding" class="form-label">Anlaşamama Sebebi</label>
                        <select class="form-control" id="reason_not_understanding" name="reason_not_understanding">
                            <option value="">Seçiniz</option>
                            <option value="Fiyat yüksek" <?php echo e(old('reason_not_understanding', $followup->reason_not_understanding)==='Fiyat yüksek' ? 'selected' : ''); ?>>Fiyat yüksek</option>
                            <option value="Mesafe Uzak" <?php echo e(old('reason_not_understanding', $followup->reason_not_understanding)==='Mesafe Uzak' ? 'selected' : ''); ?>>Mesafe Uzak</option>
                            <option value="Bayi ile yaşanan sorunlar" <?php echo e(old('reason_not_understanding', $followup->reason_not_understanding)==='Bayi ile yaşanan sorunlar' ? 'selected' : ''); ?>>Bayi ile yaşanan sorunlar</option>
                            <option value="Ekpertize ihtiyaç duymuyor" <?php echo e(old('reason_not_understanding', $followup->reason_not_understanding)==='Ekpertize ihtiyaç duymuyor' ? 'selected' : ''); ?>>Ekpertize ihtiyaç duymuyor</option>
                            <option value="Kendisi yapıyor" <?php echo e(old('reason_not_understanding', $followup->reason_not_understanding)==='Kendisi yapıyor' ? 'selected' : ''); ?>>Kendisi yapıyor</option>
                            <option value="Başka ekspertize yaptırıyor" <?php echo e(old('reason_not_understanding', $followup->reason_not_understanding)==='Başka ekspertize yaptırıyor' ? 'selected' : ''); ?>>Başka ekspertize yaptırıyor</option>
                            <option value="Değerlendirme" <?php echo e(old('reason_not_understanding', $followup->reason_not_understanding)==='Değerlendirme' ? 'selected' : ''); ?>>Değerlendirme</option>
                        </select>
                        <?php $__errorArgs = ['reason_not_understanding'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Notes Section -->
                <h5 class="mb-3 text-primary mt-4">Notlar</h5>
                <div class="row">
                    <div class="col-12 mb-3">
                        <label for="note" class="form-label">Not</label>
                        <textarea class="form-control" id="note" name="note" required><?php echo e(old('note', $followup->note)); ?></textarea>
                        <?php $__errorArgs = ['note'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 mb-3">
                        <label for="description" class="form-label">Açıklama</label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?php echo e(old('description', $followup->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><div class="text-danger"><?php echo e($message); ?></div><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <button type="submit" class="btn btn-success w-100">Güncelle</button>
                <a href="<?php echo e(route('customers.customer-followups.index', $customer->id)); ?>" class="btn btn-secondary w-100 mt-2">Geri Dön</a>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script src="<?php echo e(asset('assets')); ?>/plugins/inputmask/jquery.inputmask.bundle.js"></script>
    <script>
        $(document).ready(function(){
            $(".inputmask").inputmask({
                mask: "+99 999 999 99 99",
                showMaskOnHover: false,
                showMaskOnFocus: true,
                clearIncomplete: true,
                definitions: {
                    '9': {
                        validator: "[0-9]",
                        cardinality: 1,
                        definitionSymbol: "9"
                    }
                },
                onBeforePaste: function (pastedValue, opts) {
                    return pastedValue.replace(/[^\d\+]/g, '');
                },
                onKeyDown: function(e, buffer, caretPos, opts) {
                    var key = e.key;
                    if (!/[0-9]/.test(key) && key.length === 1) {
                        e.preventDefault();
                    }
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/customer_followups/edit.blade.php ENDPATH**/ ?>