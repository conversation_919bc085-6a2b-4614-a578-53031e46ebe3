<?php $__env->startSection('content'); ?>
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title text-center fw-bold py-2">Takip Kaydı Detayı - <?php echo e($customer->company_name); ?></h3>
                </div>
                <div class="card-body">
                    
                    <!-- Z<PERSON><PERSON> Bilgileri -->
                    <h5 class="mb-3 text-primary"><PERSON><PERSON>ret Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Takip Tarihi:</label>
                                <p class="mb-2">
                                    <?php if($followup->track_date): ?>
                                        <?php echo e(\Carbon\Carbon::parse($followup->track_date)->format('d.m.Y')); ?>

                                    <?php else: ?>
                                        Belirtilmemiş
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşme Tarihi:</label>
                                <p class="mb-2">
                                    <?php if($followup->meet_date): ?>
                                        <?php echo e(\Carbon\Carbon::parse($followup->meet_date)->format('d.m.Y')); ?>

                                    <?php else: ?>
                                        Belirtilmemiş
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşme Türü:</label>
                                <p class="mb-2">
                                    <?php switch($followup->conversation_type):
                                        case ('telefon'): ?>
                                            <span class="badge bg-info">Telefon</span>
                                            <?php break; ?>
                                        <?php case ('yerinde'): ?>
                                            <span class="badge bg-success">Yerinde</span>
                                            <?php break; ?>
                                        <?php case ('bayide'): ?>
                                            <span class="badge bg-warning">Bayide</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge bg-secondary"><?php echo e(ucfirst($followup->conversation_type ?? 'Belirtilmemiş')); ?></span>
                                    <?php endswitch; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Durum:</label>
                                <p class="mb-2">
                                    <?php switch($followup->status):
                                        case ('aktif'): ?>
                                            <span class="badge bg-success">Aktif</span>
                                            <?php break; ?>
                                        <?php case ('pasif'): ?>
                                            <span class="badge bg-warning">Pasif</span>
                                            <?php break; ?>
                                        <?php case ('eski müşteri'): ?>
                                            <span class="badge bg-secondary">Eski Müşteri</span>
                                            <?php break; ?>
                                        <?php case ('hedef müşteri'): ?>
                                            <span class="badge bg-primary">Hedef Müşteri</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge bg-secondary"><?php echo e(ucfirst($followup->status ?? 'Belirtilmemiş')); ?></span>
                                    <?php endswitch; ?>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşme Tarihindeki Durum:</label>
                                <p class="mb-2">
                                    <?php switch($followup->status_at_meeting_date):
                                        case ('aktif'): ?>
                                            <span class="badge bg-success">Aktif</span>
                                            <?php break; ?>
                                        <?php case ('sektör değişikliği'): ?>
                                            <span class="badge bg-warning">Sektör Değişikliği</span>
                                            <?php break; ?>
                                        <?php case ('taşınmış'): ?>
                                            <span class="badge bg-info">Taşınmış</span>
                                            <?php break; ?>
                                        <?php case ('başka ekspertize gidiyor'): ?>
                                            <span class="badge bg-danger">Başka Ekspertize Gidiyor</span>
                                            <?php break; ?>
                                        <?php case ('cari değişikliği'): ?>
                                            <span class="badge bg-primary">Cari Değişikliği</span>
                                            <?php break; ?>
                                        <?php case ('birden fazla plus kart'): ?>
                                            <span class="badge bg-info">Birden Fazla Plus Kart</span>
                                            <?php break; ?>
                                        <?php case ('diğer'): ?>
                                            <span class="badge bg-secondary">Diğer</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge bg-secondary"><?php echo e($followup->status_at_meeting_date ?? 'Belirtilmemiş'); ?></span>
                                    <?php endswitch; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Çalışma Türü:</label>
                                <p class="mb-2">
                                    <?php switch($followup->work_type):
                                        case ('PlusCard'): ?>
                                            <span class="badge bg-primary">PlusCard</span>
                                            <?php break; ?>
                                        <?php case ('Kurumsal'): ?>
                                            <span class="badge bg-success">Kurumsal</span>
                                            <?php break; ?>
                                        <?php case ('Filo'): ?>
                                            <span class="badge bg-warning">Filo</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge bg-secondary"><?php echo e($followup->work_type ?? 'Belirtilmemiş'); ?></span>
                                    <?php endswitch; ?>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Ziyaret Eden Kişi:</label>
                                <p class="mb-2"><?php echo e($followup->user_name ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Lokasyon Bilgileri -->
                    <h5 class="mb-3 text-primary">Lokasyon Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Şehir:</label>
                                <p class="mb-2"><?php echo e($followup->city ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İlçe:</label>
                                <p class="mb-2"><?php echo e($followup->district ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Şube Adı:</label>
                                <p class="mb-2"><?php echo e($followup->branch_name ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Firma Bilgileri -->
                    <h5 class="mb-3 text-primary">Firma Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Firma Adı:</label>
                                <p class="mb-2"><?php echo e($followup->company_name ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Mevcut Firma:</label>
                                <p class="mb-2"><?php echo e($followup->current_firm ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">E-posta:</label>
                                <p class="mb-2"><?php echo e($followup->mail ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Görüşülen Kişi Bilgileri -->
                    <h5 class="mb-3 text-primary">Görüşülen Kişi Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşülen Kişi Adı:</label>
                                <p class="mb-2"><?php echo e($followup->contact_first_name ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşülen Kişi Soyadı:</label>
                                <p class="mb-2"><?php echo e($followup->contact_last_name ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşülen Kişi Unvanı:</label>
                                <p class="mb-2"><?php echo e($followup->contact_title ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşülen Kişi Telefonu:</label>
                                <p class="mb-2"><?php echo e($followup->contact_phone ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Görüşülen Kişi Emaili:</label>
                                <p class="mb-2"><?php echo e($followup->contact_email ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Şube Bilgileri -->
                    <h5 class="mb-3 text-primary">Şube Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Mevcut Şube Sayısı:</label>
                                <p class="mb-2"><?php echo e($followup->current_branch_count ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Şube Potansiyeli:</label>
                                <p class="mb-2"><?php echo e($followup->branch_potential ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Anlaşma Bilgileri -->
                    <h5 class="mb-3 text-primary">Anlaşma Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Anlaşma Sağlandı mı?</label>
                                <p class="mb-2">
                                    <?php if($followup->agreement_status): ?>
                                        <span class="badge bg-success">Evet</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Hayır</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Pluscard Yükleme Yapıldı mı?</label>
                                <p class="mb-2">
                                    <?php if($followup->pluscard_been_loaded): ?>
                                        <span class="badge bg-success">Evet</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Hayır</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <?php if($followup->pluscard_been_loaded): ?>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Yükleme Yapılan Müşteri Sayısı:</label>
                                <p class="mb-2"><?php echo e($followup->number_of_customers_loaded ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Yükleme Miktarı:</label>
                                <p class="mb-2">
                                    <?php if($followup->loading_amount): ?>
                                        <?php echo e(number_format($followup->loading_amount, 2)); ?> ₺
                                    <?php else: ?>
                                        Belirtilmemiş
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if(!$followup->pluscard_been_loaded): ?>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Anlaşamama Sebebi:</label>
                                <p class="mb-2"><?php echo e($followup->reason_not_understanding ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Notlar -->
                    <h5 class="mb-3 text-primary">Notlar</h5>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Not:</label>
                                <p class="mb-2"><?php echo e($followup->note ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Açıklama:</label>
                                <p class="mb-2"><?php echo e($followup->description ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Sistem Bilgileri -->
                    <h5 class="mb-3 text-primary">Sistem Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Kayıt Tarihi:</label>
                                <p class="mb-2"><?php echo e(\Carbon\Carbon::parse($followup->created_at)->format('d.m.Y H:i')); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Son Güncelleme:</label>
                                <p class="mb-2"><?php echo e(\Carbon\Carbon::parse($followup->updated_at)->format('d.m.Y H:i')); ?></p>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="card-footer d-flex gap-2">
                    <a href="<?php echo e(route('customers.customer-followups.edit', [$customer->id, $followup->id])); ?>" class="btn btn-primary flex-fill">Düzenle</a>
                    <a href="<?php echo e(route('customers.customer-followups.index', $customer->id)); ?>" class="btn btn-secondary flex-fill">Geri Dön</a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-group {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 10px;
}

.info-group label {
    font-size: 0.9rem;
    margin-bottom: 5px;
    display: block;
}

.info-group p {
    font-size: 1rem;
    margin: 0;
    color: #333;
}

.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
}
</style>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/customer_followups/show.blade.php ENDPATH**/ ?>