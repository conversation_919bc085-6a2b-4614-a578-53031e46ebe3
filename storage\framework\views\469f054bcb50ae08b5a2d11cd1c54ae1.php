<?php $__env->startSection('content'); ?>
<!-- Content Header (Page header) -->
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Potansiyel Müşteri Listesi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Potansiyel Müşteri Listesi</li>
                </ol>
            </div>
        </div>
    </div><!-- /.container-fluid -->
</section>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="<?php echo e(route('potential-customers.create')); ?>" class="btn btn-primary">Yeni Potansiyel Müşteri</a>
                        </h3>

                        <div class="card-tools">
                            <?php if(session('success')): ?>
                                <div class="alert alert-success">
                                    <?php echo e(session('success')); ?>

                                </div>
                            <?php endif; ?>


                        </div>
                    </div>
                    <!-- /.card-header -->

                    <div class="card-body table-responsive p-0" style="height: 100%;">

                        <table id="potentialCustomersTable" class="table table-head-fixed text-nowrap table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Firma Adı</th>
                                    <th>Yetkili İsim</th>
                                    <th>Yetkili Soyisim</th>
                                    <th>Şirket Telefonları</th>
                                    <th>Şehir</th>
                                    <th>İlçe</th>
                                    <th>Teklif Durumu</th>
                                    <th>Teklif Tarihi</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Server-side DataTable ile doldurulacak -->
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    $('#potentialCustomersTable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?php echo e(route('potential-customers.datatable')); ?>",
            "type": "GET"
        },
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
        },
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
        "order": [[0, "desc"]],
        "columns": [
            { "data": 0, "name": "id" },
            { "data": 1, "name": "company_name" },
            { "data": 2, "name": "authorized_name" },
            { "data": 3, "name": "authorized_lastname" },
            { "data": 4, "name": "phones", "orderable": false },
            { "data": 5, "name": "city" },
            { "data": 6, "name": "district" },
            { "data": 7, "name": "offer_status" },
            { "data": 8, "name": "offer_date" },
            { "data": 9, "name": "actions", "orderable": false, "searchable": false }
        ],
        "initComplete": function () {
            // Her sütun için filtreleme input'u ekle
            this.api().columns().every(function (index) {
                var column = this;
                var title = $(column.header()).text();

                // İşlemler sütunu için filtreleme ekleme
                if (index === $('#potentialCustomersTable thead th').length - 1) {
                    return;
                }

                // Header'a filtreleme input'u ekle
                var input = $('<input type="text" placeholder="' + title + ' ara..." class="form-control form-control-sm" style="margin-top: 5px;" />')
                    .appendTo($(column.header()))
                    .on('keyup change clear', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>



<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/potential_customers/index.blade.php ENDPATH**/ ?>